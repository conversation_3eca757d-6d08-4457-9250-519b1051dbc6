import random
import json
import os
import threading
from datetime import datetime, date
from typing import Dict, List, Optional, Any
from dataclasses import dataclass, asdict
from langchain_community.chat_models import ChatOpenAI
from langchain_google_genai import ChatGoogleGenerativeAI

# Import database manager
try:
    from database_manager import DatabaseManager, DatabaseConfig, create_database_config_from_env
    DATABASE_AVAILABLE = True
except ImportError:
    DATABASE_AVAILABLE = False
    print("⚠️  Database manager không khả dụng. Sẽ sử dụng file JSON như cũ.")


@dataclass
class LLMConfig:
    """Cấu hình cho mỗi LLM"""
    name: str
    provider: str  # 'openai', 'gemini', 'groq', 'github', 'localai'
    model: str
    api_base_url: str
    api_token: str
    max_calls: int
    remaining_calls: int
    max_input_tokens: int = 3500  # Giới hạn input tokens cho mỗi chunk
    temperature: float = 0.0
    enabled: bool = True
    
    def to_dict(self):
        """Convert to dictionary for JSON serialization"""
        return asdict(self)
    
    @classmethod
    def from_dict(cls, data: dict):
        """Create from dictionary"""
        return cls(**data)


class LLMRouter:
    """Router để quản lý và chọn ngẫu nhiên giữa các LLM"""
    
    def __init__(self, config_file: str = "llm_config.json", use_database: bool = True, max_prompt_length: int = None, max_response_length: int = None):
        """
        Khởi tạo router
        
        Args:
            config_file (str): Đường dẫn file config JSON
            use_database (bool): Có sử dụng database không
            max_prompt_length (int): Giới hạn độ dài prompt khi lưu database (None = không giới hạn)
            max_response_length (int): Giới hạn độ dài response khi lưu database (None = không giới hạn)
        """
        self.config_file = config_file
        self.llm_configs: List[LLMConfig] = []
        self.lock = threading.Lock()  # Thread safety
        
        # Database configuration
        self.use_database = use_database and DATABASE_AVAILABLE
        self.db_manager = None
        
        # Text length limits for database storage
        self.max_prompt_length = max_prompt_length
        self.max_response_length = max_response_length
        
        if self.use_database:
            try:
                self.db_manager = DatabaseManager(create_database_config_from_env())
                print("✅ Database manager được khởi tạo thành công")
                # Không cần sync ở đây vì load_config sẽ xử lý
            except Exception as e:
                print(f"⚠️  Không thể khởi tạo database: {e}. Sẽ sử dụng file JSON.")
                self.use_database = False
                self.db_manager = None
        
        self.load_config()
        
        # # Thực hiện daily reset nếu cần
        # if self.use_database:
        #     self._check_and_reset_daily_calls()
    
    def load_config(self):
        """Load cấu hình từ database hoặc file JSON"""
        # Nếu sử dụng database, ưu tiên load từ database
        if self.use_database and self.db_manager:
            try:
                db_configs = self.db_manager.get_all_llm_configs()
                if db_configs:
                    # Convert từ database format sang LLMConfig objects
                    self.llm_configs = []
                    for db_config in db_configs:
                        llm_config = LLMConfig(
                            name=db_config['name'],
                            provider=db_config['provider'],
                            model=db_config['model'],
                            api_base_url=db_config['api_base_url'],
                            api_token=db_config['api_token'],
                            max_calls=db_config['max_calls'],
                            remaining_calls=self.get_remaining_calls_from_database(db_config['name']),
                            max_input_tokens=db_config['max_input_tokens'],
                            temperature=db_config['temperature'],
                            enabled=db_config['enabled']
                        )
                        self.llm_configs.append(llm_config)
                    
                    print(f"✅ Đã load {len(self.llm_configs)} LLM configs từ database")
                    return
                else:
                    print("⚠️  Database trống, sẽ load từ file JSON và sync vào database")
            except Exception as e:
                print(f"⚠️  Lỗi load từ database: {e}, sẽ fallback về file JSON")
        
        # Fallback: Load từ file JSON
        if os.path.exists(self.config_file):
            try:
                with open(self.config_file, 'r', encoding='utf-8') as f:
                    data = json.load(f)
                    self.llm_configs = [LLMConfig.from_dict(config) for config in data]
                
                # Nếu sử dụng database, cập nhật remaining_calls từ database
                if self.use_database:
                    for config in self.llm_configs:
                        config.remaining_calls = self.get_remaining_calls_from_database(config.name)
                
                print(f"✅ Đã load {len(self.llm_configs)} LLM configs từ {self.config_file}")
                
                if self.use_database:
                    print("✅ Remaining calls được cập nhật từ database")
                    # Sync configs từ file vào database
                    self._sync_configs_to_database()
                    
            except Exception as e:
                print(f"❌ Lỗi khi load config: {e}")
                self.llm_configs = []
        else:
            print(f"⚠️  File config {self.config_file} không tồn tại. Sẽ tạo mới.")
            self.create_default_config()
    
    def save_config(self):
        """Lưu cấu hình vào database và backup vào file JSON"""
        try:
            # Ưu tiên lưu vào database nếu có
            if self.use_database and self.db_manager:
                try:
                    config_dicts = [config.to_dict() for config in self.llm_configs]
                    self.db_manager.sync_llm_configs(config_dicts)
                    print("✅ Đã lưu configs vào database")
                except Exception as e:
                    print(f"⚠️  Lỗi lưu config vào database: {e}")
            
            # Luôn backup vào file JSON
            with open(self.config_file, 'w', encoding='utf-8') as f:
                json.dump([config.to_dict() for config in self.llm_configs], f, 
                         ensure_ascii=False, indent=2)
            
            if not self.use_database:
                print("✅ Đã lưu configs vào file JSON")
                    
        except Exception as e:
            print(f"❌ Lỗi khi lưu config: {e}")
    
    def create_default_config(self):
        """Tạo cấu hình mặc định"""
        default_configs = [
            LLMConfig(
                name="GPT-4o-mini",
                provider="openai",
                model="gpt-4o-mini",
                api_base_url="https://api.openai.com/v1",
                api_token="your-openai-api-key",
                max_calls=100,
                remaining_calls=100,
                max_input_tokens=3500
            ),
            LLMConfig(
                name="Gemini Pro",
                provider="gemini",
                model="gemini-1.5-pro",
                api_base_url="",  # Gemini không cần base URL
                api_token="your-gemini-api-key",
                max_calls=50,
                remaining_calls=50,
                max_input_tokens=4000
            ),
            LLMConfig(
                name="Groq Llama",
                provider="groq",
                model="llama-3.3-70b-versatile",
                api_base_url="https://api.groq.com/openai/v1",
                api_token="your-groq-api-key",
                max_calls=200,
                remaining_calls=200,
                max_input_tokens=8000
            ),
            LLMConfig(
                name="GitHub GPT-4o",
                provider="github",
                model="gpt-4o",
                api_base_url="https://models.inference.ai.azure.com",
                api_token="your-github-token",
                max_calls=75,
                remaining_calls=75,
                max_input_tokens=8000
            )
        ]
        
        self.llm_configs = default_configs
        self.save_config()
        
        # Nếu sử dụng database, reset daily calls cho configs mới
        if self.use_database and self.db_manager:
            try:
                self.db_manager.reset_daily_calls()
                print("✅ Đã khởi tạo daily calls trong database")
            except Exception as e:
                print(f"⚠️  Lỗi khởi tạo daily calls: {e}")
        
        print(f"✅ Đã tạo file config mặc định: {self.config_file}")
        print("🔧 Vui lòng cập nhật API tokens trong file config hoặc database!")
    
    def add_llm(self, config: LLMConfig):
        """Thêm LLM mới"""
        with self.lock:
            self.llm_configs.append(config)
            self.save_config()
            print(f"✅ Đã thêm LLM: {config.name}")
    
    def remove_llm(self, name: str):
        """Xóa LLM theo tên"""
        with self.lock:
            self.llm_configs = [config for config in self.llm_configs if config.name != name]
            self.save_config()
            print(f"✅ Đã xóa LLM: {name}")
    
    def update_llm_calls(self, name: str, remaining_calls: int):
        """Cập nhật số lượt call còn lại"""
        with self.lock:
            for config in self.llm_configs:
                if config.name == name:
                    config.remaining_calls = remaining_calls
                    self.save_config()
                    break
    
    def get_available_llms(self) -> List[LLMConfig]:
        """Lấy danh sách LLM còn lượt call và được enable"""
        available_llms = []
        
        for config in self.llm_configs:
            if not config.enabled:
                continue
            
            # Lấy remaining calls từ database nếu có
            if self.use_database:
                remaining_calls = self.get_remaining_calls_from_database(config.name)
            else:
                remaining_calls = config.remaining_calls
            
            if remaining_calls > 0:
                # Cập nhật remaining_calls cho config
                config.remaining_calls = remaining_calls
                available_llms.append(config)
        
        return available_llms
    
    def select_random_llm(self) -> Optional[LLMConfig]:
        """Chọn ngẫu nhiên một LLM có thể sử dụng"""
        available_llms = self.get_available_llms()
        
        if not available_llms:
            print("❌ Không có LLM nào khả dụng!")
            return None
        
        # Weighted random selection based on remaining calls
        weights = [config.remaining_calls for config in available_llms]
        selected = random.choices(available_llms, weights=weights, k=1)[0]
        
        print(f"🎯 Đã chọn LLM: {selected.name} (còn {selected.remaining_calls} lượt)")
        return selected
    
    def create_llm_instance(self, config: LLMConfig):
        """Tạo instance LLM từ config"""
        try:
            if config.provider == 'openai':
                return ChatOpenAI(
                    model=config.model,
                    temperature=config.temperature,
                    openai_api_base=config.api_base_url,
                    openai_api_key=config.api_token
                )
            elif config.provider == 'gemini':
                return ChatGoogleGenerativeAI(
                    model=config.model,
                    temperature=config.temperature,
                    google_api_key=config.api_token,
                    convert_system_message_to_human=True
                )
            elif config.provider in ['groq', 'github', 'localai']:
                print(config.api_base_url+ " -> "+config.api_token+"-> "+config.model)
                return ChatOpenAI(
                    model=config.model,
                    temperature=config.temperature,
                    openai_api_base=config.api_base_url,
                    openai_api_key=config.api_token
                )
            else:
                raise Exception(f"Provider không được hỗ trợ: {config.provider}")
                
        except Exception as e:
            print(f"❌ Lỗi tạo LLM instance cho {config.name}: {e}")
            return None
    
    def make_request(self, prompt: str, **kwargs) -> Optional[Dict[str, Any]]:
        """
        Gửi request đến một LLM được chọn ngẫu nhiên
        
        Args:
            prompt (str): Prompt để gửi
            **kwargs: Các tham số khác cho LLM
            
        Returns:
            Dict hoặc None: Kết quả từ LLM hoặc None nếu thất bại
        """
        start_time = datetime.now()
        selected_config = None
        
        with self.lock:
            selected_config = self.select_random_llm()
            
            if not selected_config:
                return None
            
            # Giảm số lượt call trong memory
            selected_config.remaining_calls -= 1
            self.save_config()
            
            print(f"📤 Gửi request đến {selected_config.name} (còn {selected_config.remaining_calls} lượt)")
        
        # Chuẩn bị log data
        log_data = {
            'llm_name': selected_config.name,
            'provider': selected_config.provider,
            'model': selected_config.model,
            'prompt_text': prompt[:self.max_prompt_length] if self.max_prompt_length else prompt,
            'request_data': kwargs,
            'input_tokens': 0,
            'output_tokens': 0,
            'total_tokens': 0,
            'success': False,
            'error_message': '',
            'processing_time_ms': 0,
            'response_text': '',
            'response_data': {}
        }
        
        try:
            # Tạo LLM instance
            llm = self.create_llm_instance(selected_config)
            if not llm:
                raise Exception("Không thể tạo LLM instance")
            
            # Gửi request
            from langchain.prompts import ChatPromptTemplate
            from langchain.chains import LLMChain
            
            chat_prompt = ChatPromptTemplate.from_template(prompt)
            chain = LLMChain(llm=llm, prompt=chat_prompt)
            
            result = chain.invoke(kwargs)
            
            # Tính thời gian xử lý
            processing_time = datetime.now() - start_time
            processing_time_ms = int(processing_time.total_seconds() * 1000)
            
            # Cập nhật log data với kết quả thành công
            log_data.update({
                'success': True,
                'response_text': result['text'][:self.max_response_length] if self.max_response_length else result['text'],
                'response_data': {'full_result': str(result)[:1000] if len(str(result)) > 1000 else str(result)},
                'processing_time_ms': processing_time_ms
            })
            
            # Log vào database
            if self.use_database and self.db_manager:
                try:
                    self.db_manager.log_request(log_data)
                except Exception as e:
                    print(f"⚠️  Lỗi log request vào database: {e}")
            
            return {
                'success': True,
                'llm_name': selected_config.name,
                'provider': selected_config.provider,
                'model': selected_config.model,
                'remaining_calls': selected_config.remaining_calls,
                'response': result['text'],
                'timestamp': datetime.now().isoformat(),
                'processing_time_ms': processing_time_ms
            }
            
        except Exception as e:
            print(f"❌ Lỗi khi gửi request đến {selected_config.name}: {e}")
            
            # Tính thời gian xử lý cho trường hợp lỗi
            processing_time = datetime.now() - start_time
            processing_time_ms = int(processing_time.total_seconds() * 1000)
            
            # Hoàn lại số lượt call nếu thất bại
            with self.lock:
                selected_config.remaining_calls += 1
                self.save_config()
            
            # Cập nhật log data với lỗi
            log_data.update({
                'success': False,
                'error_message': str(e)[:1000] if len(str(e)) > 1000 else str(e),
                'processing_time_ms': processing_time_ms
            })
            
            # Log vào database ngay cả khi lỗi
            if self.use_database and self.db_manager:
                try:
                    self.db_manager.log_request(log_data)
                except Exception as log_error:
                    print(f"⚠️  Lỗi log request lỗi vào database: {log_error}")
            
            return {
                'success': False,
                'llm_name': selected_config.name,
                'provider': selected_config.provider,
                'model': selected_config.model,
                'error': str(e),
                'timestamp': datetime.now().isoformat(),
                'processing_time_ms': processing_time_ms
            }
    
    def get_status(self) -> Dict[str, Any]:
        """Lấy trạng thái của tất cả LLM"""
        total_calls = sum(config.remaining_calls for config in self.llm_configs)
        available_count = len(self.get_available_llms())
        
        status = {
            'total_llms': len(self.llm_configs),
            'available_llms': available_count,
            'total_remaining_calls': total_calls,
            'llms': []
        }
        
        for config in self.llm_configs:
            llm_status = {
                'name': config.name,
                'provider': config.provider,
                'model': config.model,
                'max_calls': config.max_calls,
                'remaining_calls': config.remaining_calls,
                'enabled': config.enabled,
                'usage_percentage': ((config.max_calls - config.remaining_calls) / config.max_calls * 100) if config.max_calls > 0 else 0
            }
            status['llms'].append(llm_status)
        
        return status
    
    def print_status(self):
        """In trạng thái ra console"""
        status = self.get_status()
        
        print("\n" + "="*60)
        print("🤖 TRẠNG THÁI LLM ROUTER")
        print("="*60)
        print(f"📊 Tổng số LLM: {status['total_llms']}")
        print(f"✅ LLM khả dụng: {status['available_llms']}")
        print(f"🔢 Tổng lượt call còn lại: {status['total_remaining_calls']}")
        print("\n📋 CHI TIẾT TỪNG LLM:")
        
        for llm in status['llms']:
            status_icon = "✅" if llm['enabled'] and llm['remaining_calls'] > 0 else "❌"
            usage = llm['usage_percentage']
            
            print(f"\n{status_icon} {llm['name']}")
            print(f"   Provider: {llm['provider']}")
            print(f"   Model: {llm['model']}")
            print(f"   Còn lại: {llm['remaining_calls']}/{llm['max_calls']} lượt")
            print(f"   Đã dùng: {usage:.1f}%")
            print(f"   Trạng thái: {'Khả dụng' if llm['enabled'] and llm['remaining_calls'] > 0 else 'Không khả dụng'}")
        
        print("="*60)
    
    def reset_all_calls(self):
        """Reset tất cả số lượt call về max"""
        with self.lock:
            for config in self.llm_configs:
                config.remaining_calls = config.max_calls
            self.save_config()
            
            # Reset trong database nếu có
            if self.use_database:
                self.reset_daily_calls_database()
                
            print("✅ Đã reset tất cả số lượt call")
    
    def enable_llm(self, name: str):
        """Bật LLM"""
        with self.lock:
            for config in self.llm_configs:
                if config.name == name:
                    config.enabled = True
                    self.save_config()
                    print(f"✅ Đã bật LLM: {name}")
                    return
            print(f"❌ Không tìm thấy LLM: {name}")
    
    def disable_llm(self, name: str):
        """Tắt LLM"""
        with self.lock:
            for config in self.llm_configs:
                if config.name == name:
                    config.enabled = False
                    self.save_config()
                    print(f"✅ Đã tắt LLM: {name}")
                    return
            print(f"❌ Không tìm thấy LLM: {name}")
    
    def get_max_input_tokens(self, llm_name: str = None) -> int:
        """
        Lấy max_input_tokens cho LLM cụ thể hoặc tính toán giá trị tối ưu
        
        Args:
            llm_name (str): Tên LLM cụ thể, nếu None sẽ tính trung bình của các LLM khả dụng
            
        Returns:
            int: Giá trị max_input_tokens
        """
        if llm_name:
            # Tìm LLM cụ thể
            for config in self.llm_configs:
                if config.name == llm_name:
                    return config.max_input_tokens
            # Nếu không tìm thấy, trả về giá trị mặc định
            return 3500
        
        # Tính toán dựa trên các LLM khả dụng
        available_llms = self.get_available_llms()
        if not available_llms:
            return 3500  # Giá trị mặc định
        
        # Lấy giá trị trung bình (có thể thay đổi logic này)
        total_tokens = sum(config.max_input_tokens for config in available_llms)
        avg_tokens = total_tokens // len(available_llms)
        
        return avg_tokens

    def _check_and_reset_daily_calls(self):
        """Kiểm tra và reset daily calls nếu cần"""
        if not self.db_manager:
            return
        
        try:
            today = date.today()
            # Kiểm tra xem có cần reset không (có thể check last reset date)
            self.db_manager.reset_daily_calls(today)
        except Exception as e:
            print(f"⚠️  Lỗi reset daily calls: {e}")
    
    def _sync_configs_to_database(self):
        """Đồng bộ configs từ file JSON vào database"""
        if not self.db_manager:
            return
        
        try:
            # Load từ file JSON trước
            temp_configs = []
            if os.path.exists(self.config_file):
                with open(self.config_file, 'r', encoding='utf-8') as f:
                    data = json.load(f)
                    temp_configs = [config for config in data]
            
            if temp_configs:
                self.db_manager.sync_llm_configs(temp_configs)
                print(f"✅ Đã đồng bộ {len(temp_configs)} configs vào database")
        except Exception as e:
            print(f"⚠️  Lỗi đồng bộ configs: {e}")
    
    def get_remaining_calls_from_database(self, llm_name: str) -> int:
        """Lấy remaining calls từ database"""
        if not self.db_manager:
            return 0
        
        try:
            return self.db_manager.get_daily_remaining_calls(llm_name)
        except Exception as e:
            print(f"⚠️  Lỗi lấy remaining calls từ database: {e}")
            return 0
    
    def reset_daily_calls_database(self, target_date: date = None) -> bool:
        """Reset daily calls trong database"""
        if not self.use_database or not self.db_manager:
            return False
        
        try:
            return self.db_manager.reset_daily_calls(target_date)
        except Exception as e:
            print(f"❌ Lỗi reset daily calls trong database: {e}")
            return False
    
    def get_database_stats(self, days: int = 7) -> Dict[str, Any]:
        """Lấy thống kê từ database"""
        if not self.use_database or not self.db_manager:
            return {}
        
        try:
            from datetime import timedelta
            start_date = date.today() - timedelta(days=days)
            return self.db_manager.get_request_stats(start_date, date.today())
        except Exception as e:
            print(f"❌ Lỗi lấy thống kê database: {e}")
            return {}
    
    def cleanup_old_database_logs(self, days_to_keep: int = 30) -> bool:
        """Xóa log cũ trong database"""
        if not self.use_database or not self.db_manager:
            return False
        
        try:
            return self.db_manager.cleanup_old_logs(days_to_keep)
        except Exception as e:
            print(f"❌ Lỗi cleanup database logs: {e}")
            return False

# Utility functions
def create_router_from_env(use_database: bool = True, max_prompt_length: int = None, max_response_length: int = None):
    """Tạo router từ environment variables"""
    import os
    
    # Đọc từ environment variables nếu không được truyền vào
    if max_prompt_length is None:
        env_prompt_length = os.getenv('MAX_PROMPT_LENGTH', '').strip()
        max_prompt_length = int(env_prompt_length) if env_prompt_length.isdigit() else None
    
    if max_response_length is None:
        env_response_length = os.getenv('MAX_RESPONSE_LENGTH', '').strip()
        max_response_length = int(env_response_length) if env_response_length.isdigit() else None
    
    router = LLMRouter(
        use_database=use_database,
        max_prompt_length=max_prompt_length,
        max_response_length=max_response_length
    )
    
    # # Load từ environment nếu có
    # try:
    #     from dotenv import load_dotenv
    #     load_dotenv()
        
    #     # Ví dụ load config từ env vars
    #     openai_key = os.getenv('OPENAI_API_KEY')
    #     gemini_key = os.getenv('GEMINI_API_KEY')
    #     groq_key = os.getenv('GROQ_API_KEY')
        
    #     # Cập nhật tokens từ env
    #     for config in router.llm_configs:
    #         if config.provider == 'openai' and openai_key:
    #             config.api_token = openai_key
    #         elif config.provider == 'gemini' and gemini_key:
    #             config.api_token = gemini_key
    #         elif config.provider == 'groq' and groq_key:
    #             config.api_token = groq_key
        
    #     router.save_config()
    #     print("✅ Đã cập nhật tokens từ environment variables")
        
    # except ImportError:
    #     print("⚠️  python-dotenv không được cài đặt")
    
    return router


# Example usage và testing
if __name__ == "__main__":
    # Tạo router
    router = LLMRouter()
    
    # Hiển thị trạng thái
    router.print_status()
    
    # Test request (uncomment để test)
    """
    result = router.make_request(
        "Xin chào! Bạn là ai?",
        input="test"
    )
    
    if result:
        print("\n📨 KẾT QUẢ REQUEST:")
        print(f"LLM: {result['llm_name']}")
        print(f"Response: {result['response'][:100]}...")
    """
    
    # Hiển thị trạng thái sau khi test
    # router.print_status()
