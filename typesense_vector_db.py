# ============================================================================
# TYPESENSE VECTOR DATABASE LIBRARY
# ============================================================================

import os
import json
import uuid
import hashlib
from typing import List, Dict, Any, Optional
from datetime import datetime
import typesense
from dotenv import load_dotenv
import docx
from langchain_openai import OpenAIEmbeddings
from llm_router import create_router_from_env

# Load environment variables
load_dotenv()

class TypesenseVectorDB:
    """
    Thư viện để sử dụng Typesense làm vector database cho việc embedding văn bản
    """
    
    def __init__(self, collection_name: str = "documents"):
        """
        Khởi tạo kết nối Typesense
        
        Args:
            collection_name (str): Tên collection trong Typesense
        """
        self.collection_name = collection_name
        
        # Khởi tạo Typesense client từ .env
        self.client = typesense.Client({
            'nodes': [{
                'host': os.getenv('TYPESENSE_HOST'),
                'port': os.getenv('TYPESENSE_PORT'),
                'protocol': os.getenv('TYPESENSE_PROTOCOL')
            }],
            'api_key': os.getenv('TYPESENSE_API_KEY'),
            'connection_timeout_seconds': int(os.getenv('TYPESENSE_TIMEOUT', '60'))
        })
        
        # Khởi tạo embeddings
        self.embeddings = OpenAIEmbeddings(
            model=os.getenv('EMBEDDINGS_MODEL', 'text-embedding-ada-002'),
            openai_api_base=os.getenv('EMBEDDINGS_API_BASE'),
            openai_api_key=os.getenv('EMBEDDINGS_API_KEY')
        )
        
        # Khởi tạo LLM router
        self.llm_router = create_router_from_env()
        
        # Tạo collection nếu chưa tồn tại
        self._create_collection_if_not_exists()
    
    def _create_collection_if_not_exists(self):
        """Tạo collection nếu chưa tồn tại"""
        try:
            # Kiểm tra xem collection đã tồn tại chưa
            self.client.collections[self.collection_name].retrieve()
            print(f"✅ Collection '{self.collection_name}' đã tồn tại")
        except typesense.exceptions.ObjectNotFound:
            # Tạo collection mới
            schema = {
                'name': self.collection_name,
                'fields': [
                    {'name': 'id', 'type': 'string'},
                    {'name': 'content', 'type': 'string'},
                    {'name': 'title', 'type': 'string', 'optional': True},
                    {'name': 'source_file', 'type': 'string', 'optional': True},
                    {'name': 'chunk_index', 'type': 'int32', 'optional': True},
                    {'name': 'metadata', 'type': 'object', 'optional': True},
                    {'name': 'embedding', 'type': 'float[]', 'num_dim': 1536},  # OpenAI embedding dimension
                    {'name': 'created_at', 'type': 'int64'},
                    {'name': 'content_hash', 'type': 'string'}
                ]
            }
            
            self.client.collections.create(schema)
            print(f"✅ Đã tạo collection '{self.collection_name}'")
    
    def read_docx(self, file_path: str) -> str:
        """
        Đọc nội dung file Word
        
        Args:
            file_path (str): Đường dẫn đến file Word
            
        Returns:
            str: Nội dung văn bản
        """
        doc = docx.Document(file_path)
        full_text = []
        
        # Đọc các đoạn văn thông thường
        for para in doc.paragraphs:
            text = para.text.strip()
            if text:
                full_text.append(text)
        
        # Đọc nội dung trong bảng
        for table in doc.tables:
            for row in table.rows:
                row_text = []
                for cell in row.cells:
                    cell_text = cell.text.strip()
                    if cell_text:
                        row_text.append(cell_text)
                if row_text:
                    full_text.append('\t'.join(row_text))
        
        return '\n'.join(full_text)
    
    def chunk_text(self, text: str, chunk_size: int = 1000, overlap: int = 200) -> List[str]:
        """
        Chia văn bản thành các chunks nhỏ hơn
        
        Args:
            text (str): Văn bản cần chia
            chunk_size (int): Kích thước mỗi chunk
            overlap (int): Số ký tự overlap giữa các chunks
            
        Returns:
            List[str]: Danh sách các chunks
        """
        chunks = []
        start = 0
        
        while start < len(text):
            end = start + chunk_size
            chunk = text[start:end]
            
            # Tìm điểm cắt tốt hơn (cuối câu hoặc đoạn)
            if end < len(text):
                last_period = chunk.rfind('.')
                last_newline = chunk.rfind('\n')
                cut_point = max(last_period, last_newline)
                
                if cut_point > start + chunk_size // 2:  # Chỉ cắt nếu không quá ngắn
                    chunk = text[start:start + cut_point + 1]
                    end = start + cut_point + 1
            
            chunks.append(chunk.strip())
            start = end - overlap
            
            if start >= len(text):
                break
        
        return [chunk for chunk in chunks if chunk.strip()]
    
    def generate_content_hash(self, content: str) -> str:
        """Tạo hash cho nội dung để tránh duplicate"""
        return hashlib.md5(content.encode('utf-8')).hexdigest()
    
    def import_docx_to_typesense(self, file_path: str, title: str = None, metadata: Dict = None) -> Dict[str, Any]:
        """
        Import dữ liệu từ file docx vào Typesense
        
        Args:
            file_path (str): Đường dẫn đến file Word
            title (str): Tiêu đề tài liệu (optional)
            metadata (Dict): Metadata bổ sung (optional)
            
        Returns:
            Dict[str, Any]: Kết quả import
        """
        try:
            print(f"📖 Đang đọc file: {file_path}")
            
            # Đọc nội dung file
            content = self.read_docx(file_path)
            
            if not content.strip():
                return {
                    "success": False,
                    "error": "File không có nội dung hoặc không đọc được"
                }
            
            print(f"📄 Đã đọc {len(content)} ký tự")
            
            # Chia thành chunks
            chunks = self.chunk_text(content)
            print(f"🔪 Đã chia thành {len(chunks)} chunks")
            
            # Tạo embeddings và lưu vào Typesense
            documents = []
            successful_imports = 0
            
            for i, chunk in enumerate(chunks):
                try:
                    # Kiểm tra duplicate bằng content hash
                    content_hash = self.generate_content_hash(chunk)
                    
                    # Tìm kiếm document với hash tương tự
                    search_result = self.client.collections[self.collection_name].documents.search({
                        'q': '*',
                        'filter_by': f'content_hash:={content_hash}',
                        'per_page': 1
                    })
                    
                    if search_result['found'] > 0:
                        print(f"⚠️  Chunk {i+1} đã tồn tại, bỏ qua")
                        continue
                    
                    # Tạo embedding
                    print(f"🔄 Đang tạo embedding cho chunk {i+1}/{len(chunks)}")
                    embedding = self.embeddings.embed_query(chunk)
                    
                    # Tạo document
                    doc = {
                        'id': str(uuid.uuid4()),
                        'content': chunk,
                        'title': title or os.path.basename(file_path),
                        'source_file': os.path.basename(file_path),
                        'chunk_index': i,
                        'metadata': metadata or {},
                        'embedding': embedding,
                        'created_at': int(datetime.now().timestamp()),
                        'content_hash': content_hash
                    }
                    
                    documents.append(doc)
                    
                except Exception as e:
                    print(f"❌ Lỗi xử lý chunk {i+1}: {e}")
                    continue
            
            # Bulk import vào Typesense
            if documents:
                print(f"💾 Đang import {len(documents)} documents vào Typesense...")
                
                # Import từng batch để tránh timeout
                batch_size = 10
                for i in range(0, len(documents), batch_size):
                    batch = documents[i:i + batch_size]
                    try:
                        result = self.client.collections[self.collection_name].documents.import_(batch)
                        successful_imports += len([r for r in result if r.get('success', False)])
                        print(f"✅ Đã import batch {i//batch_size + 1}: {len(batch)} documents")
                    except Exception as e:
                        print(f"❌ Lỗi import batch {i//batch_size + 1}: {e}")
            
            return {
                "success": True,
                "total_chunks": len(chunks),
                "imported_documents": successful_imports,
                "skipped_duplicates": len(chunks) - len(documents),
                "file_path": file_path,
                "title": title or os.path.basename(file_path)
            }
            
        except Exception as e:
            return {
                "success": False,
                "error": f"Lỗi import file: {str(e)}"
            }

    def search_similar_documents(self, query: str, limit: int = 10, threshold: float = 0.7) -> Dict[str, Any]:
        """
        Tìm kiếm documents tương tự dựa trên vector similarity

        Args:
            query (str): Câu hỏi/truy vấn
            limit (int): Số lượng kết quả tối đa
            threshold (float): Ngưỡng similarity (0-1)

        Returns:
            Dict[str, Any]: Kết quả tìm kiếm
        """
        try:
            print(f"🔍 Đang tìm kiếm: '{query}'")

            # Tạo embedding cho query
            query_embedding = self.embeddings.embed_query(query)

            # Tìm kiếm vector similarity trong Typesense
            search_params = {
                'q': '*',
                'vector_query': f'embedding:([{",".join(map(str, query_embedding))}], k:{limit})',
                'per_page': limit
            }

            result = self.client.collections[self.collection_name].documents.search(search_params)

            # Xử lý kết quả
            documents = []
            for hit in result.get('hits', []):
                doc = hit['document']
                # Typesense trả về similarity score trong vector_distance (càng nhỏ càng giống)
                # Chuyển đổi thành similarity score (càng lớn càng giống)
                similarity = 1 - hit.get('vector_distance', 1.0)

                if similarity >= threshold:
                    documents.append({
                        'id': doc['id'],
                        'content': doc['content'],
                        'title': doc.get('title', ''),
                        'source_file': doc.get('source_file', ''),
                        'chunk_index': doc.get('chunk_index', 0),
                        'similarity': similarity,
                        'metadata': doc.get('metadata', {})
                    })

            print(f"✅ Tìm thấy {len(documents)} documents phù hợp")

            return {
                "success": True,
                "query": query,
                "total_found": len(documents),
                "documents": documents,
                "threshold": threshold
            }

        except Exception as e:
            return {
                "success": False,
                "error": f"Lỗi tìm kiếm: {str(e)}"
            }

    def search_and_answer(self, question: str, limit: int = 5, threshold: float = 0.7) -> Dict[str, Any]:
        """
        Tìm kiếm documents liên quan và sử dụng LLM để trả lời câu hỏi

        Args:
            question (str): Câu hỏi
            limit (int): Số lượng documents tối đa để tham khảo
            threshold (float): Ngưỡng similarity

        Returns:
            Dict[str, Any]: Câu trả lời và thông tin liên quan
        """
        try:
            print(f"❓ Đang trả lời câu hỏi: '{question}'")

            # Tìm kiếm documents liên quan
            search_result = self.search_similar_documents(question, limit, threshold)

            if not search_result["success"]:
                return search_result

            documents = search_result["documents"]

            if not documents:
                return {
                    "success": True,
                    "question": question,
                    "answer": "Không tìm thấy thông tin liên quan trong cơ sở dữ liệu.",
                    "sources": [],
                    "confidence": 0.0
                }

            # Tạo context từ các documents tìm được
            context_parts = []
            sources = []

            for i, doc in enumerate(documents):
                context_parts.append(f"[Nguồn {i+1}] {doc['content']}")
                sources.append({
                    'source_file': doc['source_file'],
                    'title': doc['title'],
                    'similarity': doc['similarity'],
                    'chunk_index': doc['chunk_index']
                })

            context = "\n\n".join(context_parts)

            # Tạo prompt cho LLM
            prompt = f"""Dựa trên thông tin sau đây, hãy trả lời câu hỏi một cách chính xác và chi tiết:

THÔNG TIN THAM KHẢO:
{context}

CÂU HỎI: {question}

Hãy trả lời dựa trên thông tin được cung cấp. Nếu thông tin không đủ để trả lời, hãy nói rõ điều đó. Trích dẫn nguồn khi cần thiết."""

            # Sử dụng LLM router để trả lời
            llm_result = self.llm_router.make_request(prompt)

            if llm_result and llm_result.get('success', False):
                answer = llm_result['response']
                confidence = sum(doc['similarity'] for doc in documents) / len(documents)

                return {
                    "success": True,
                    "question": question,
                    "answer": answer,
                    "sources": sources,
                    "confidence": confidence,
                    "llm_used": llm_result.get('llm_name', 'Unknown'),
                    "total_documents_found": len(documents)
                }
            else:
                return {
                    "success": False,
                    "error": f"Lỗi từ LLM: {llm_result.get('error', 'Unknown error') if llm_result else 'No response'}"
                }

        except Exception as e:
            return {
                "success": False,
                "error": f"Lỗi trả lời câu hỏi: {str(e)}"
            }

    def delete_documents_by_file(self, source_file: str) -> Dict[str, Any]:
        """
        Xóa tất cả documents từ một file cụ thể

        Args:
            source_file (str): Tên file nguồn

        Returns:
            Dict[str, Any]: Kết quả xóa
        """
        try:
            # Tìm tất cả documents từ file này
            search_result = self.client.collections[self.collection_name].documents.search({
                'q': '*',
                'filter_by': f'source_file:={source_file}',
                'per_page': 1000  # Giả sử không có quá 1000 chunks từ 1 file
            })

            if search_result['found'] == 0:
                return {
                    "success": True,
                    "message": f"Không tìm thấy documents từ file '{source_file}'",
                    "deleted_count": 0
                }

            # Xóa từng document
            deleted_count = 0
            for hit in search_result['hits']:
                doc_id = hit['document']['id']
                try:
                    self.client.collections[self.collection_name].documents[doc_id].delete()
                    deleted_count += 1
                except Exception as e:
                    print(f"❌ Lỗi xóa document {doc_id}: {e}")

            return {
                "success": True,
                "message": f"Đã xóa {deleted_count} documents từ file '{source_file}'",
                "deleted_count": deleted_count
            }

        except Exception as e:
            return {
                "success": False,
                "error": f"Lỗi xóa documents: {str(e)}"
            }

    def get_collection_stats(self) -> Dict[str, Any]:
        """
        Lấy thống kê về collection

        Returns:
            Dict[str, Any]: Thông tin thống kê
        """
        try:
            # Lấy thông tin collection
            collection_info = self.client.collections[self.collection_name].retrieve()

            # Đếm tổng số documents
            search_result = self.client.collections[self.collection_name].documents.search({
                'q': '*',
                'per_page': 0  # Chỉ lấy count
            })

            total_documents = search_result['found']

            # Thống kê theo source file
            files_stats = {}
            if total_documents > 0:
                # Lấy tất cả documents để thống kê
                all_docs = self.client.collections[self.collection_name].documents.search({
                    'q': '*',
                    'per_page': total_documents
                })

                for hit in all_docs['hits']:
                    doc = hit['document']
                    source_file = doc.get('source_file', 'Unknown')
                    if source_file not in files_stats:
                        files_stats[source_file] = 0
                    files_stats[source_file] += 1

            return {
                "success": True,
                "collection_name": self.collection_name,
                "total_documents": total_documents,
                "files_stats": files_stats,
                "schema": collection_info
            }

        except Exception as e:
            return {
                "success": False,
                "error": f"Lỗi lấy thống kê: {str(e)}"
            }
