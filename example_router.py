"""
Example sử dụng LLM Router để xử lý file Word

Hướng dẫn sử dụng:
1. <PERSON><PERSON>p nhật API tokens trong file llm_config.json
2. Chạy script với các tùy chọn khác nhau
"""

from llm_router import LLMRouter, LLMConfig, create_router_from_env
from ai import extract_sections_from_docx_with_router, main_with_router
import json

def setup_example_router():
    """Tạo router với cấu hình ví dụ"""
    
    # Tạo router
    router = LLMRouter("example_llm_config.json")
    
    # Thêm các LLM example (thay thế bằng API keys thật)
    example_configs = [
        LLMConfig(
            name="OpenAI GPT-4o-mini",
            provider="openai",
            model="gpt-4o-mini",
            api_base_url="https://api.openai.com/v1",
            api_token="sk-your-openai-api-key-here",
            max_calls=50,
            remaining_calls=50,
            temperature=0.0
        ),
        LLMConfig(
            name="Groq Llama 3.3",
            provider="groq", 
            model="llama-3.3-70b-versatile",
            api_base_url="https://api.groq.com/openai/v1",
            api_token="gsk_your-groq-api-key-here",
            max_calls=100,
            remaining_calls=100,
            temperature=0.0
        ),
        LLMConfig(
            name="GitHub GPT-4o",
            provider="github",
            model="gpt-4o",
            api_base_url="https://models.inference.ai.azure.com",
            api_token="ghp_your-github-token-here",
            max_calls=75,
            remaining_calls=75,
            temperature=0.0
        ),
        LLMConfig(
            name="Gemini 1.5 Pro",
            provider="gemini",
            model="gemini-1.5-pro",
            api_base_url="",
            api_token="your-gemini-api-key-here",
            max_calls=30,
            remaining_calls=30,
            temperature=0.0
        ),
        LLMConfig(
            name="LocalAI Custom",
            provider="localai",
            model="custom-model",
            api_base_url="http://localhost:8080/v1",
            api_token="not-needed",
            max_calls=1000,
            remaining_calls=1000,
            temperature=0.0,
            enabled=False  # Tắt mặc định vì có thể không có LocalAI
        )
    ]
    
    # Clear existing configs và add new ones
    #router.llm_configs = example_configs
    #router.save_config()
    
    print("✅ Đã tạo example router với 5 LLM configs")
    return router

def demo_router_usage():
    """Demo cách sử dụng router"""
    
    print("🚀 DEMO LLM ROUTER")
    print("="*50)
    
    # 1. Tạo router
    router = setup_example_router()
    
    # 2. Hiển thị trạng thái
    print("\n1. TRẠNG THÁI BAN ĐẦU:")
    router.print_status()
    
    # 3. Test random selection
    print("\n2. TEST RANDOM SELECTION:")
    for i in range(5):
        selected = router.select_random_llm()
        if selected:
            print(f"   Lần {i+1}: {selected.name} (còn {selected.remaining_calls} lượt)")
        else:
            print(f"   Lần {i+1}: Không có LLM khả dụng")
    
    # 4. Demo enable/disable
    print("\n3. TEST ENABLE/DISABLE:")
    router.disable_llm("Gemini 1.5 Pro")
    router.enable_llm("LocalAI Custom")
    
    available = router.get_available_llms()
    print(f"   LLM khả dụng sau khi enable/disable: {len(available)}")
    for llm in available:
        print(f"   - {llm.name}")
    
    # 5. Test making requests (commented out để tránh gọi API thật)
    print("\n4. SIMULATION REQUESTS:")
    print("   (Giả lập 10 requests)")
    
    for i in range(10):
        selected = router.select_random_llm()
        if selected:
            # Simulate request
            with router.lock:
                selected.remaining_calls -= 1
                router.save_config()
            print(f"   Request {i+1}: {selected.name} (còn {selected.remaining_calls} lượt)")
        else:
            print(f"   Request {i+1}: Không có LLM khả dụng")
    
    # 6. Trạng thái cuối
    print("\n5. TRẠNG THÁI SAU KHI SIMULATION:")
    router.print_status()
    
    # 7. Reset
    print("\n6. RESET VÀ TRẠNG THÁI CUỐI:")
    router.reset_all_calls()
    router.print_status()

def demo_with_word_file():
    """Demo xử lý file Word với router (cần file Word thật)"""
    
    print("\n" + "="*60)
    print("🤖 DEMO XỬ LÝ FILE WORD VỚI ROUTER")
    print("="*60)
    
    # Kiểm tra có file Word không
    test_files = ['input.docx', '1.docx', '2.docx', '3.docx']
    word_file = None
    
    for file in test_files:
        try:
            with open(file, 'rb'):
                word_file = file
                break
        except FileNotFoundError:
            continue
    
    if not word_file:
        print("❌ Không tìm thấy file Word nào để test")
        print("💡 Tạo file Word tên 'input.docx' để test")
        return
    
    print(f"📄 Sử dụng file: {word_file}")
    
    # Tạo router
    router = setup_example_router()
    
    # Cập nhật API keys từ environment nếu có
    try:
        import os
        from dotenv import load_dotenv
        load_dotenv()
        
        # Update với real API keys nếu có
        for config in router.llm_configs:
            if config.provider == 'openai':
                key = os.getenv('OPENAI_API_KEY')
                if key and key != 'your-api-key-here':
                    config.api_token = key
                    config.enabled = True
            elif config.provider == 'groq':
                key = os.getenv('GROQ_API_KEY')
                if key and key != 'your-api-key-here':
                    config.api_token = key
                    config.enabled = True
            elif config.provider == 'gemini':
                key = os.getenv('GEMINI_API_KEY')
                if key and key != 'your-api-key-here':
                    config.api_token = key
                    config.enabled = True
        
        router.save_config()
        
    except Exception as e:
        print(f"⚠️  Không thể load environment variables: {e}")
    
    # Kiểm tra có LLM nào khả dụng không
    available = router.get_available_llms()
    if not available:
        print("❌ Không có LLM nào khả dụng (chưa có API keys)")
        print("💡 Cập nhật API keys trong file example_llm_config.json")
        return
    
    print(f"✅ Có {len(available)} LLM khả dụng")
    
    # Xử lý file Word
    try:
        main_with_router(word_file, router)
    except Exception as e:
        print(f"❌ Lỗi khi xử lý file Word: {e}")

if __name__ == "__main__":
    import sys
    
    if len(sys.argv) > 1:
        if sys.argv[1] == '--demo':
            demo_router_usage()
        elif sys.argv[1] == '--word':
            demo_with_word_file()
        elif sys.argv[1] == '--both':
            demo_router_usage()
            demo_with_word_file()
        else:
            print("Usage:")
            print("  python example_router.py --demo     # Demo router basic")
            print("  python example_router.py --word     # Demo với file Word")
            print("  python example_router.py --both     # Demo cả hai")
    else:
        print("🚀 LLM ROUTER EXAMPLE")
        print("=" * 30)
        print("Chọn demo:")
        print("1. Demo router basic")
        print("2. Demo với file Word") 
        print("3. Demo cả hai")
        
        choice = input("\nNhập lựa chọn (1-3): ").strip()
        
        if choice == '1':
            demo_router_usage()
        elif choice == '2':
            demo_with_word_file()
        elif choice == '3':
            demo_router_usage()
            demo_with_word_file()
        else:
            print("❌ Lựa chọn không hợp lệ")
