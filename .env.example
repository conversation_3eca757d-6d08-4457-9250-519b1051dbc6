GITHUB_OPENAI_API_KEY=**********************************************************************************************
GITHUB_OPENAI_API_BASE=https://models.inference.ai.azure.com

GROQ_API_KEY=********************************************************
GROQ_API_BASE=https://api.groq.com/openai/v1

# Model configurations
GITHUB_MODELS=gpt-4.1,gpt-4.1-mini
GROQ_MODELS=llama-3.3-70b-versatile,meta-llama/llama-4-maverick-17b-128e-instruct

# Backward compatibility - default model configuration
OPENAI_MODEL=llama-3.3-70b-versatile
OPENAI_API_KEY=********************************************************
OPENAI_API_BASE=https://api.groq.com/openai/v1

OPENAI_TEMPERATURE=0.3
EMBEDDINGS_MODEL=all-MiniLM-L6-v2
EMBEDDINGS_API_BASE=https://models.ai.vietec.vn:8443/v1
EMBEDDINGS_API_KEY=abc

# Gemini Configuration
GEMINI_MODELS=gemini-1.5-pro,gemini-1.5-flash,gemini-1.0-pro
GEMINI_API_KEY=your_gemini_api_key_here

# Word file max token limit
WORD_FILE_MAX_TOKENS=12000  # Số tokens tối đa cho file Word upload, vượt quá sẽ trả về lỗi
